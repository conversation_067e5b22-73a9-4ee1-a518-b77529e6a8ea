<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Feature\AdminFieldShowPageBootstrapTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" tests="7" assertions="65" errors="0" failures="0" skipped="0" time="0.138753">
    <testcase name="admin_field_show_page_loads_with_bootstrap_styling" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="67" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="9" time="0.083443">
      <system-out>✅ Bootstrap styling is properly implemented
</system-out>
    </testcase>
    <testcase name="field_show_page_displays_all_content_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="90" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="9" time="0.009236">
      <system-out>✅ All field content displays correctly
</system-out>
    </testcase>
    <testcase name="field_show_page_displays_amenities_with_bootstrap" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="115" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="9" time="0.009787">
      <system-out>✅ Amenities display correctly with Bootstrap styling
</system-out>
    </testcase>
    <testcase name="field_show_page_displays_bookings_table" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="141" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="6" time="0.009369">
      <system-out>✅ Bookings table displays correctly
</system-out>
    </testcase>
    <testcase name="navigation_buttons_work_with_bootstrap_styling" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="161" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="10" time="0.009020">
      <system-out>✅ Navigation buttons have proper Bootstrap styling
</system-out>
    </testcase>
    <testcase name="delete_modal_uses_bootstrap_modal" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="187" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="13" time="0.009329">
      <system-out>✅ Delete modal uses proper Bootstrap modal
</system-out>
    </testcase>
    <testcase name="field_show_page_has_proper_admin_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="216" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="9" time="0.008569">
      <system-out>✅ Admin layout is properly implemented
</system-out>
    </testcase>
  </testsuite>
</testsuites>
