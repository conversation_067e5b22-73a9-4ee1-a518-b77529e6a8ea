<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="/Users/<USER>/Projects/Clients/FPMP/smp_online/phpunit.xml" tests="254" assertions="996" errors="13" failures="0" skipped="0" time="2.446419">
    <testsuite name="Unit" tests="70" assertions="272" errors="0" failures="0" skipped="0" time="0.339795">
      <testsuite name="Tests\Unit\ExampleTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ExampleTest.php" tests="1" assertions="1" errors="0" failures="0" skipped="0" time="0.000782">
        <testcase name="test_that_true_is_true" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ExampleTest.php" line="12" class="Tests\Unit\ExampleTest" classname="Tests.Unit.ExampleTest" assertions="1" time="0.000782"/>
      </testsuite>
      <testsuite name="Tests\Unit\FieldAvailabilityConflictTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" tests="14" assertions="39" errors="0" failures="0" skipped="0" time="0.105864">
        <testcase name="it_detects_no_conflict_for_adjacent_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="35" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.063556"/>
        <testcase name="it_detects_overlapping_conflicts_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="54" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="7" time="0.003930"/>
        <testcase name="it_detects_no_conflict_for_non_overlapping_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="78" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="4" time="0.003411"/>
        <testcase name="it_excludes_specified_reservation_from_conflict_check" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="99" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.003543"/>
        <testcase name="it_ignores_cancelled_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="140" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.004027"/>
        <testcase name="it_validates_time_format_inputs" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="159" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.002909"/>
        <testcase name="it_validates_end_time_format" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="170" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.002696"/>
        <testcase name="it_validates_time_order" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="180" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.002624"/>
        <testcase name="it_validates_same_start_and_end_time" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="190" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="2" time="0.002612"/>
        <testcase name="it_handles_multiple_existing_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="200" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="4" time="0.003577"/>
        <testcase name="it_only_checks_conflicts_for_same_field" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="235" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.003317"/>
        <testcase name="it_only_checks_conflicts_for_same_date" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="254" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="1" time="0.003159"/>
        <testcase name="it_accepts_various_valid_time_formats" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="273" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="4" time="0.002842"/>
        <testcase name="it_handles_edge_case_boundary_times" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityConflictTest.php" line="285" class="Tests\Unit\FieldAvailabilityConflictTest" classname="Tests.Unit.FieldAvailabilityConflictTest" assertions="4" time="0.003660"/>
      </testsuite>
      <testsuite name="Tests\Unit\FieldAvailabilityServiceTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" tests="12" assertions="60" errors="0" failures="0" skipped="0" time="0.055915">
        <testcase name="it_checks_if_field_is_available" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="36" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="1" time="0.004671"/>
        <testcase name="it_detects_unavailable_field_due_to_existing_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="46" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="2" time="0.003414"/>
        <testcase name="it_checks_working_hours_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="67" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="4" time="0.002780"/>
        <testcase name="it_detects_conflicting_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="83" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="5" time="0.003313"/>
        <testcase name="it_gets_available_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="107" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="6" time="0.003736"/>
        <testcase name="it_excludes_unavailable_slots_from_available_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="126" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="1" time="0.003780"/>
        <testcase name="it_gets_field_availability_calendar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="150" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="9" time="0.007778"/>
        <testcase name="it_gets_all_fields_availability" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="171" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="6" time="0.005407"/>
        <testcase name="it_validates_duration" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="191" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="3" time="0.003093"/>
        <testcase name="it_finds_next_available_slot" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="199" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="4" time="0.003685"/>
        <testcase name="it_validates_reservation_comprehensively" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="212" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="6" time="0.003457"/>
        <testcase name="it_gets_field_statistics" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="235" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="13" time="0.010801"/>
      </testsuite>
      <testsuite name="Tests\Unit\ReservationCostServiceTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" tests="10" assertions="92" errors="0" failures="0" skipped="0" time="0.042562">
        <testcase name="it_calculates_total_cost_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="38" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="1" time="0.005253"/>
        <testcase name="it_provides_detailed_cost_breakdown" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="45" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="17" time="0.003186"/>
        <testcase name="it_calculates_multi_slot_cost" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="72" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="6" time="0.003531"/>
        <testcase name="it_provides_field_cost_comparison" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="93" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="8" time="0.004014"/>
        <testcase name="it_calculates_duration_savings" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="115" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="7" time="0.004304"/>
        <testcase name="it_calculates_monthly_cost_for_recurring_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="141" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="14" time="0.003923"/>
        <testcase name="it_gets_user_cost_statistics" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="164" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="11" time="0.006118"/>
        <testcase name="it_calculates_peak_hour_cost" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="208" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="10" time="0.003905"/>
        <testcase name="it_validates_cost_calculation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="228" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="8" time="0.004578"/>
        <testcase name="it_gets_reservation_estimate" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationCostServiceTest.php" line="252" class="Tests\Unit\ReservationCostServiceTest" classname="Tests.Unit.ReservationCostServiceTest" assertions="10" time="0.003750"/>
      </testsuite>
      <testsuite name="Tests\Unit\ReservationModelTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" tests="17" assertions="49" errors="0" failures="0" skipped="0" time="0.084401">
        <testcase name="it_calculates_total_cost_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="36" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="1" time="0.005806"/>
        <testcase name="it_formats_time_range_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="48" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="1" time="0.004320"/>
        <testcase name="it_formats_date_time_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="59" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="1" time="0.004935"/>
        <testcase name="it_returns_correct_customer_display_name" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="73" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.005865"/>
        <testcase name="it_returns_correct_status_colors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="91" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="4" time="0.005965"/>
        <testcase name="it_determines_if_reservation_can_be_cancelled" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="107" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="4" time="0.007148"/>
        <testcase name="it_determines_if_reservation_can_be_modified" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="139" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="4" time="0.006703"/>
        <testcase name="it_auto_confirms_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="171" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.004751"/>
        <testcase name="it_cancels_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="185" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.006910"/>
        <testcase name="it_detects_conflicts_with_existing_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="199" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.004821"/>
        <testcase name="it_calculates_duration_in_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="232" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="1" time="0.003872"/>
        <testcase name="it_checks_if_within_working_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="243" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.003996"/>
        <testcase name="it_validates_duration_for_field" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="263" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="2" time="0.004029"/>
        <testcase name="scopes_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="281" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="11" time="0.005071"/>
        <testcase name="factory_today_method_creates_reservation_for_today" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="329" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="3" time="0.003232"/>
        <testcase name="today_scope_only_returns_todays_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="347" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="4" time="0.003599"/>
        <testcase name="multiple_today_reservations_are_all_found" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/ReservationModelTest.php" line="377" class="Tests\Unit\ReservationModelTest" classname="Tests.Unit.ReservationModelTest" assertions="3" time="0.003378"/>
      </testsuite>
      <testsuite name="Tests\Unit\UtilityModelTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" tests="16" assertions="31" errors="0" failures="0" skipped="0" time="0.050271">
        <testcase name="utility_has_fillable_attributes" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="16" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="4" time="0.004950"/>
        <testcase name="utility_casts_is_active_to_boolean" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="29" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002802"/>
        <testcase name="utility_has_fields_relationship" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="38" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.003991"/>
        <testcase name="active_scope_returns_only_active_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="50" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002911"/>
        <testcase name="inactive_scope_returns_only_inactive_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="62" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002882"/>
        <testcase name="display_name_attribute_includes_status" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="74" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002786"/>
        <testcase name="status_badge_class_returns_correct_classes" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="91" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002713"/>
        <testcase name="status_text_returns_correct_text" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="101" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002732"/>
        <testcase name="can_be_deleted_returns_true_when_no_fields_use_utility" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="111" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="1" time="0.002734"/>
        <testcase name="can_be_deleted_returns_false_when_fields_use_utility" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="119" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="1" time="0.002914"/>
        <testcase name="fields_count_attribute_returns_correct_count" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="129" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="1" time="0.003273"/>
        <testcase name="activate_method_sets_utility_to_active" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="140" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002814"/>
        <testcase name="deactivate_method_sets_utility_to_inactive" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="151" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.004254"/>
        <testcase name="toggle_status_method_toggles_active_status" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="162" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="3" time="0.002998"/>
        <testcase name="utility_uses_soft_deletes" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="176" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="2" time="0.002907"/>
        <testcase name="utility_name_is_required_for_mass_assignment" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/UtilityModelTest.php" line="188" class="Tests\Unit\UtilityModelTest" classname="Tests.Unit.UtilityModelTest" assertions="1" time="0.002612"/>
      </testsuite>
    </testsuite>
    <testsuite name="Feature" tests="184" assertions="724" errors="13" failures="0" skipped="0" time="2.106624">
      <testsuite name="Tests\Feature\AdminFieldNavigationTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" tests="6" assertions="34" errors="0" failures="0" skipped="0" time="0.070657">
        <testcase name="back_to_fields_button_works_from_show_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="35" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="4" time="0.019964">
          <system-out>✅ Back to Fields button navigation works correctly
</system-out>
        </testcase>
        <testcase name="edit_field_button_works_from_show_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="55" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="5" time="0.010979">
          <system-out>✅ Edit Field button navigation works correctly
</system-out>
        </testcase>
        <testcase name="delete_field_modal_elements_are_present" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="76" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="6" time="0.006872">
          <system-out>✅ Delete Field modal elements are present
</system-out>
        </testcase>
        <testcase name="field_show_page_breadcrumb_navigation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="96" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="5" time="0.006537">
          <system-out>✅ Breadcrumb navigation is present and correct
</system-out>
        </testcase>
        <testcase name="field_show_page_has_proper_action_buttons" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="113" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="7" time="0.006459">
          <system-out>✅ Action buttons are present and functional
</system-out>
        </testcase>
        <testcase name="complete_navigation_flow_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldNavigationTest.php" line="134" class="Tests\Feature\AdminFieldNavigationTest" classname="Tests.Feature.AdminFieldNavigationTest" assertions="7" time="0.019846">
          <system-out>✅ Step 1: Index page loads
✅ Step 2: Show page loads from View button
✅ Step 3: Edit page loads from Edit button
✅ Step 4: Back to show page works
✅ Step 5: Back to index works
✅ Complete navigation flow works perfectly
</system-out>
        </testcase>
      </testsuite>
      <testsuite name="Tests\Feature\AdminFieldShowPageBootstrapTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" tests="7" assertions="0" errors="7" failures="0" skipped="0" time="0.000000">
        <testcase name="admin_field_show_page_loads_with_bootstrap_styling" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="54" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::admin_field_show_page_loads_with_bootstrap_styling
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="field_show_page_displays_all_content_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="77" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::field_show_page_displays_all_content_correctly
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="field_show_page_displays_amenities_with_bootstrap" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="102" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::field_show_page_displays_amenities_with_bootstrap
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="field_show_page_displays_bookings_table" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="126" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::field_show_page_displays_bookings_table
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="navigation_buttons_work_with_bootstrap_styling" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="146" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::navigation_buttons_work_with_bootstrap_styling
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="delete_modal_uses_bootstrap_modal" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="172" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::delete_modal_uses_bootstrap_modal
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
        <testcase name="field_show_page_has_proper_admin_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php" line="200" class="Tests\Feature\AdminFieldShowPageBootstrapTest" classname="Tests.Feature.AdminFieldShowPageBootstrapTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageBootstrapTest::field_show_page_has_proper_admin_layout
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Bootstrap Test Field, Soccer, Professional soccer field with Bootstrap styling, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageBootstrapTest.php:28</error>
        </testcase>
      </testsuite>
      <testsuite name="Tests\Feature\AdminFieldShowPageTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" tests="5" assertions="0" errors="5" failures="0" skipped="0" time="0.000000">
        <testcase name="field_id_11_database_check" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" line="41" class="Tests\Feature\AdminFieldShowPageTest" classname="Tests.Feature.AdminFieldShowPageTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageTest::field_id_11_database_check
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Test Soccer Field 11, Soccer, Professional soccer field with artificial turf and LED lighting for testing field ID 11, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php:27</error>
        </testcase>
        <testcase name="admin_can_access_field_show_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" line="79" class="Tests\Feature\AdminFieldShowPageTest" classname="Tests.Feature.AdminFieldShowPageTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageTest::admin_can_access_field_show_page
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Test Soccer Field 11, Soccer, Professional soccer field with artificial turf and LED lighting for testing field ID 11, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php:27</error>
        </testcase>
        <testcase name="field_show_page_has_correct_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" line="99" class="Tests\Feature\AdminFieldShowPageTest" classname="Tests.Feature.AdminFieldShowPageTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageTest::field_show_page_has_correct_layout
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Test Soccer Field 11, Soccer, Professional soccer field with artificial turf and LED lighting for testing field ID 11, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php:27</error>
        </testcase>
        <testcase name="field_show_page_displays_all_content" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" line="116" class="Tests\Feature\AdminFieldShowPageTest" classname="Tests.Feature.AdminFieldShowPageTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageTest::field_show_page_displays_all_content
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Test Soccer Field 11, Soccer, Professional soccer field with artificial turf and LED lighting for testing field ID 11, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php:27</error>
        </testcase>
        <testcase name="field_show_page_navigation_buttons_work" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php" line="147" class="Tests\Feature\AdminFieldShowPageTest" classname="Tests.Feature.AdminFieldShowPageTest" assertions="0" time="0.000000">
          <error type="Illuminate\Database\QueryException">Tests\Feature\AdminFieldShowPageTest::field_show_page_navigation_buttons_work
Illuminate\Database\QueryException: SQLSTATE[HY000]: General error: 1 table fields has no column named amenities (Connection: sqlite, SQL: insert into "fields" ("name", "type", "description", "hourly_rate", "capacity", "status", "amenities", "opening_time", "closing_time", "updated_at", "created_at") values (Test Soccer Field 11, Soccer, Professional soccer field with artificial turf and LED lighting for testing field ID 11, 85, 24, Active, ?, 08:00, 22:00, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:301
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:295
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldShowPageTest.php:27</error>
        </testcase>
      </testsuite>
      <testsuite name="Tests\Feature\AdminFieldViewTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" tests="7" assertions="33" errors="0" failures="0" skipped="0" time="0.053254">
        <testcase name="admin_can_access_fields_index_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="40" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="5" time="0.007142">
          <system-out>✅ Admin fields index page loads successfully
✅ Field listing displays correctly
✅ View and Edit buttons are present
</system-out>
        </testcase>
        <testcase name="admin_can_view_field_details_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="57" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="10" time="0.006666">
          <system-out>✅ Field show page loads successfully
✅ All field information displays correctly
✅ Navigation buttons are present
</system-out>
        </testcase>
        <testcase name="field_show_page_displays_amenities_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="85" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="4" time="0.008737">
          <system-out>✅ Amenities section displays correctly
</system-out>
        </testcase>
        <testcase name="field_show_page_displays_statistics" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="108" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="3" time="0.006851">
          <system-out>✅ Field statistics display correctly
</system-out>
        </testcase>
        <testcase name="field_show_page_has_proper_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="123" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="5" time="0.006520">
          <system-out>✅ Admin layout loads correctly
✅ No layout errors detected
</system-out>
        </testcase>
        <testcase name="view_field_button_navigation_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="143" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="3" time="0.009299">
          <system-out>✅ View Field button navigation works correctly
</system-out>
        </testcase>
        <testcase name="edit_field_button_navigation_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminFieldViewTest.php" line="162" class="Tests\Feature\AdminFieldViewTest" classname="Tests.Feature.AdminFieldViewTest" assertions="3" time="0.008039">
          <system-out>✅ Edit Field button navigation works correctly
</system-out>
        </testcase>
      </testsuite>
      <testsuite name="Tests\Feature\AdminUserEditTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" tests="10" assertions="37" errors="0" failures="0" skipped="0" time="0.060930">
        <testcase name="test_admin_can_access_user_edit_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="29" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="3" time="0.009007"/>
        <testcase name="test_non_admin_cannot_access_user_edit_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="44" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="1" time="0.006183"/>
        <testcase name="test_admin_can_update_user_basic_information" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="58" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="7" time="0.008239"/>
        <testcase name="test_admin_can_update_user_password" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="89" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="3" time="0.007629"/>
        <testcase name="test_admin_can_lock_user_account" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="116" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="4" time="0.005013"/>
        <testcase name="test_admin_can_unlock_user_account" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="142" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="4" time="0.004805"/>
        <testcase name="test_admin_cannot_remove_own_admin_role" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="169" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="3" time="0.004450"/>
        <testcase name="test_admin_cannot_lock_own_account" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="190" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="3" time="0.004424"/>
        <testcase name="test_validation_errors_are_handled" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="211" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="6" time="0.004705"/>
        <testcase name="test_password_confirmation_validation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AdminUserEditTest.php" line="230" class="Tests\Feature\AdminUserEditTest" classname="Tests.Feature.AdminUserEditTest" assertions="3" time="0.006476"/>
      </testsuite>
      <testsuite name="Tests\Feature\AmenityCrudTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" tests="10" assertions="28" errors="1" failures="0" skipped="0" time="0.047199">
        <testcase name="admin_can_view_amenities_index" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="31" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="0" time="0.005325">
          <error type="Illuminate\Database\UniqueConstraintViolationException">Tests\Feature\AmenityCrudTest::admin_can_view_amenities_index
Illuminate\Database\UniqueConstraintViolationException: SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: amenities.name (Connection: sqlite, SQL: insert into "amenities" ("name", "description", "icon_class", "is_active", "updated_at", "created_at") values (First Aid Station, Emergency first aid and medical supplies, ri-archive-line, 1, 2025-07-07 08:09:48, 2025-07-07 08:09:48))

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:817
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:776
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:557
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:521
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php:32
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:3796
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php:2205
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1410
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1375
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php:1214
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:350
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php:271
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:345
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Factories/Factory.php:305
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php:34</error>
        </testcase>
        <testcase name="non_admin_cannot_access_amenities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="47" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="1" time="0.003861">
          <system-out>✅ Non-admin users are properly restricted
</system-out>
        </testcase>
        <testcase name="admin_can_view_create_amenity_form" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="58" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="4" time="0.005309">
          <system-out>✅ Admin can view create amenity form
</system-out>
        </testcase>
        <testcase name="admin_can_create_amenity_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="72" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.004134">
          <system-out>✅ Admin can create amenity successfully
</system-out>
        </testcase>
        <testcase name="amenity_creation_requires_valid_data" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="98" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="4" time="0.004571">
          <system-out>✅ Amenity creation validation works correctly
</system-out>
        </testcase>
        <testcase name="admin_can_view_amenity_details" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="124" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="4" time="0.005945">
          <system-out>✅ Admin can view amenity details
</system-out>
        </testcase>
        <testcase name="admin_can_edit_amenity" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="143" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.005875">
          <system-out>✅ Admin can view edit amenity form
</system-out>
        </testcase>
        <testcase name="admin_can_update_amenity_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="158" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.004241">
          <system-out>✅ Admin can update amenity successfully
</system-out>
        </testcase>
        <testcase name="admin_can_delete_unused_amenity" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="190" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.003956">
          <system-out>✅ Admin can delete unused amenity
</system-out>
        </testcase>
        <testcase name="admin_cannot_delete_amenity_in_use" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="206" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.003982">
          <system-out>✅ Admin cannot delete amenity in use
</system-out>
        </testcase>
      </testsuite>
      <testsuite name="Tests\Feature\AuthenticationTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" tests="6" assertions="20" errors="0" failures="0" skipped="0" time="1.086246">
        <testcase name="test_login_with_email" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="17" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="5" time="0.008483"/>
        <testcase name="test_login_with_username" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="37" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="5" time="0.005447"/>
        <testcase name="test_admin_middleware_protection" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="57" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="1" time="0.003564"/>
        <testcase name="test_admin_access_to_user_management" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="69" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="1" time="0.007749"/>
        <testcase name="test_account_lockout_after_failed_attempts" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="81" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="2" time="1.048326"/>
        <testcase name="test_role_based_dashboard_redirection" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="105" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="6" time="0.012678"/>
      </testsuite>
      <testsuite name="Tests\Feature\BookingEditViewTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" tests="8" assertions="39" errors="0" failures="0" skipped="0" time="0.069381">
        <testcase name="test_booking_edit_page_loads_with_correct_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="27" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="10" time="0.016377"/>
        <testcase name="test_booking_owner_can_access_edit_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="61" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="2" time="0.011254"/>
        <testcase name="test_unauthorized_user_cannot_access_edit_page" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="81" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="1" time="0.007113"/>
        <testcase name="test_edit_form_contains_required_fields" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="100" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="12" time="0.008778"/>
        <testcase name="test_edit_form_shows_current_booking_data" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="134" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="6" time="0.008221"/>
        <testcase name="test_javascript_functions_are_included" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="163" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="4" time="0.007686"/>
        <testcase name="test_past_bookings_cannot_be_edited" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="187" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="2" time="0.005139"/>
        <testcase name="test_completed_bookings_cannot_be_edited" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/BookingEditViewTest.php" line="207" class="Tests\Feature\BookingEditViewTest" classname="Tests.Feature.BookingEditViewTest" assertions="2" time="0.004813"/>
      </testsuite>
      <testsuite name="Tests\Feature\CalendarModalReservationTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" tests="8" assertions="41" errors="0" failures="0" skipped="0" time="0.055752">
        <testcase name="calendar_page_loads_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="37" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="3" time="0.010115"/>
        <testcase name="calendar_modal_redirects_to_reservation_form_with_data" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="47" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="6" time="0.012315"/>
        <testcase name="ajax_reservation_creation_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="65" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="10" time="0.005099"/>
        <testcase name="ajax_reservation_returns_validation_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="97" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="4" time="0.004277"/>
        <testcase name="ajax_reservation_handles_availability_conflicts" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="115" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="4" time="0.005081"/>
        <testcase name="reservation_form_pre_populates_from_url_parameters" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="145" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="4" time="0.010932"/>
        <testcase name="calendar_modal_handles_working_hours_validation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="179" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="5" time="0.004051"/>
        <testcase name="calendar_modal_handles_duration_validation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="202" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="5" time="0.003882"/>
      </testsuite>
      <testsuite name="Tests\Feature\CarbonTypeCastingTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" tests="7" assertions="25" errors="0" failures="0" skipped="0" time="0.037125">
        <testcase name="reservation_creation_handles_string_duration_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="37" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="2" time="0.005770"/>
        <testcase name="ajax_reservation_creation_handles_string_duration_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="66" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="2" time="0.004396"/>
        <testcase name="reservation_update_handles_string_duration_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="95" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="2" time="0.005560"/>
        <testcase name="availability_check_handles_string_duration_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="134" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="4" time="0.005051"/>
        <testcase name="cost_estimate_handles_string_parameters" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="153" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="8" time="0.004298"/>
        <testcase name="working_hours_validation_works_with_string_duration" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="178" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="4" time="0.007171"/>
        <testcase name="duration_validation_works_with_string_duration" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CarbonTypeCastingTest.php" line="201" class="Tests\Feature\CarbonTypeCastingTest" classname="Tests.Feature.CarbonTypeCastingTest" assertions="3" time="0.004879"/>
      </testsuite>
      <testsuite name="Tests\Feature\ClientDashboardLayoutTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" tests="13" assertions="76" errors="0" failures="0" skipped="0" time="0.080053">
        <testcase name="client_dashboard_loads_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="44" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="4" time="0.007686"/>
        <testcase name="client_dashboard_has_proper_layout_structure" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="56" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="12" time="0.006138"/>
        <testcase name="client_dashboard_shows_appropriate_buttons_with_proper_spacing" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="83" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.005815"/>
        <testcase name="client_header_dropdown_shows_appropriate_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="105" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="7" time="0.005679"/>
        <testcase name="normal_user_header_dropdown_shows_reservation_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="125" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.005747"/>
        <testcase name="admin_header_dropdown_shows_reservation_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="140" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.005677"/>
        <testcase name="client_dashboard_has_responsive_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="155" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005576"/>
        <testcase name="client_dashboard_quick_actions_are_properly_spaced" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="173" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005698"/>
        <testcase name="client_dashboard_feature_cards_have_proper_button_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="191" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005401"/>
        <testcase name="client_dashboard_welcome_section_has_proper_icon_spacing" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="209" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.005503"/>
        <testcase name="client_dashboard_access_notice_is_informative" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="226" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.007349"/>
        <testcase name="client_dashboard_support_function_is_included" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="240" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="3" time="0.005990"/>
        <testcase name="client_dashboard_maintains_consistency_with_other_dashboards" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="255" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.007793"/>
      </testsuite>
      <testsuite name="Tests\Feature\ExampleTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ExampleTest.php" tests="1" assertions="1" errors="0" failures="0" skipped="0" time="0.005664">
        <testcase name="test_the_application_returns_a_successful_response" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ExampleTest.php" line="13" class="Tests\Feature\ExampleTest" classname="Tests.Feature.ExampleTest" assertions="1" time="0.005664"/>
      </testsuite>
      <testsuite name="Tests\Feature\FPMPReservationSystemTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" tests="16" assertions="51" errors="0" failures="0" skipped="0" time="0.084181">
        <testcase name="user_can_view_reservations_index" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="46" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.006245"/>
        <testcase name="user_can_view_reservation_creation_form" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="57" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.006186"/>
        <testcase name="user_can_create_reservation_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="68" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.005254"/>
        <testcase name="user_cannot_create_reservation_for_past_date" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="107" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="2" time="0.004427"/>
        <testcase name="user_cannot_create_reservation_outside_working_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="122" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="2" time="0.004420"/>
        <testcase name="user_cannot_create_overlapping_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="137" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="2" time="0.004631"/>
        <testcase name="user_can_view_their_reservation_details" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="162" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.006909"/>
        <testcase name="user_cannot_view_other_users_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="180" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="1" time="0.004818"/>
        <testcase name="user_can_edit_their_future_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="194" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="3" time="0.008753"/>
        <testcase name="user_can_update_their_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="214" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="5" time="0.005542"/>
        <testcase name="user_cannot_edit_reservation_within_24_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="254" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="2" time="0.004152"/>
        <testcase name="user_can_cancel_their_future_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="270" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.004161"/>
        <testcase name="user_cannot_cancel_reservation_within_24_hours" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="294" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="3" time="0.004005"/>
        <testcase name="cost_calculation_is_accurate" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="315" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="1" time="0.004252"/>
        <testcase name="availability_check_api_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="335" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="4" time="0.004525"/>
        <testcase name="navigation_links_are_accessible" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FPMPReservationSystemTest.php" line="353" class="Tests\Feature\FPMPReservationSystemTest" classname="Tests.Feature.FPMPReservationSystemTest" assertions="6" time="0.005901"/>
      </testsuite>
      <testsuite name="Tests\Feature\Feature\UtilityCrudTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" tests="20" assertions="70" errors="0" failures="0" skipped="0" time="0.092976">
        <testcase name="admin_can_view_utilities_index" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="30" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.009157"/>
        <testcase name="employee_cannot_access_utilities_index" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="44" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="1" time="0.003734"/>
        <testcase name="guest_cannot_access_utilities_index" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="53" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="2" time="0.003509"/>
        <testcase name="admin_can_view_create_utility_form" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="61" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="5" time="0.005858"/>
        <testcase name="admin_can_create_utility_with_valid_data" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="75" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="2" time="0.004082"/>
        <testcase name="utility_creation_requires_name" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="97" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.006188"/>
        <testcase name="utility_name_must_be_unique" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="114" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="2" time="0.004280"/>
        <testcase name="admin_can_view_utility_details" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="131" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.006562"/>
        <testcase name="admin_can_view_edit_utility_form" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="144" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.005968"/>
        <testcase name="admin_can_update_utility" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="158" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="6" time="0.004388"/>
        <testcase name="admin_can_delete_utility_not_in_use" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="187" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.003889"/>
        <testcase name="admin_cannot_delete_utility_in_use_by_fields" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="199" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="4" time="0.003959"/>
        <testcase name="admin_can_perform_bulk_activate_action" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="214" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="5" time="0.004251"/>
        <testcase name="admin_can_perform_bulk_deactivate_action" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="234" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="5" time="0.004330"/>
        <testcase name="admin_can_perform_bulk_delete_action_on_unused_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="254" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="5" time="0.004434"/>
        <testcase name="admin_cannot_bulk_delete_utilities_in_use" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="273" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="5" time="0.004374"/>
        <testcase name="utility_defaults_to_active_when_created" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="296" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="2" time="0.003727"/>
        <testcase name="field_utility_relationship_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="317" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="3" time="0.003606"/>
        <testcase name="utility_scopes_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="331" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="2" time="0.003316"/>
        <testcase name="utility_model_methods_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Feature/UtilityCrudTest.php" line="344" class="Tests\Feature\Feature\UtilityCrudTest" classname="Tests.Feature.Feature.UtilityCrudTest" assertions="6" time="0.003364"/>
      </testsuite>
      <testsuite name="Tests\Feature\FieldUtilityRelationshipTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" tests="11" assertions="54" errors="0" failures="0" skipped="0" time="0.067714">
        <testcase name="admin_can_create_field_with_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="30" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="7" time="0.009696"/>
        <testcase name="admin_can_update_field_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="63" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="7" time="0.005482"/>
        <testcase name="admin_can_remove_all_utilities_from_field" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="103" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="3" time="0.004973"/>
        <testcase name="field_show_page_displays_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="132" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="6" time="0.007467"/>
        <testcase name="field_show_page_displays_no_utilities_message" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="155" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="3" time="0.006891"/>
        <testcase name="field_edit_form_shows_utility_checkboxes" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="169" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="8" time="0.007916"/>
        <testcase name="field_create_form_shows_utility_checkboxes" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="189" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="8" time="0.006790"/>
        <testcase name="only_active_utilities_are_shown_in_forms" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="207" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="3" time="0.006486"/>
        <testcase name="field_utility_validation_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="222" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="2" time="0.004083"/>
        <testcase name="field_utility_relationship_is_many_to_many" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="241" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="5" time="0.004273"/>
        <testcase name="deleting_utility_removes_field_relationships" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/FieldUtilityRelationshipTest.php" line="261" class="Tests\Feature\FieldUtilityRelationshipTest" classname="Tests.Feature.FieldUtilityRelationshipTest" assertions="2" time="0.003656"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutErrorFixTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" tests="12" assertions="41" errors="0" failures="0" skipped="0" time="0.052033">
        <testcase name="user_can_logout_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="28" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.005408"/>
        <testcase name="logout_via_header_dropdown_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="48" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.008186"/>
        <testcase name="logout_via_sidebar_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="69" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.006246"/>
        <testcase name="header_displays_correctly_during_logout_process" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="90" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.005475"/>
        <testcase name="dashboard_pages_handle_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="104" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003396"/>
        <testcase name="admin_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="115" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003218"/>
        <testcase name="client_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="126" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003249"/>
        <testcase name="main_dashboard_route_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="137" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003295"/>
        <testcase name="header_shows_fallback_values_when_user_is_null" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="148" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="1" time="0.002823"/>
        <testcase name="multiple_logout_attempts_dont_cause_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="158" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="4" time="0.003976"/>
        <testcase name="logout_clears_session_properly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="177" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="4" time="0.003356"/>
        <testcase name="logout_regenerates_csrf_token" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="198" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="2" time="0.003404"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutNullSafetyTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" tests="14" assertions="66" errors="0" failures="0" skipped="0" time="0.087723">
        <testcase name="client_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="44" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.007841"/>
        <testcase name="normal_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="68" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.008558"/>
        <testcase name="admin_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="92" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.006849"/>
        <testcase name="header_template_handles_null_user_during_logout_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="116" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.006151"/>
        <testcase name="sidebar_template_handles_null_user_during_logout_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="135" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="3" time="0.006086"/>
        <testcase name="client_user_sees_clean_header_without_reservation_buttons" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="151" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="5" time="0.005412"/>
        <testcase name="normal_user_sees_reservation_buttons_in_header" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="170" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005380"/>
        <testcase name="admin_user_sees_reservation_buttons_in_header" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="186" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005480"/>
        <testcase name="client_user_sees_clean_sidebar_without_fpmp_features" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="202" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="7" time="0.005254"/>
        <testcase name="normal_user_sees_fpmp_features_in_sidebar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="225" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005621"/>
        <testcase name="admin_user_sees_all_features_in_sidebar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="243" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="5" time="0.005905"/>
        <testcase name="multiple_logout_attempts_dont_cause_null_reference_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="262" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.004086"/>
        <testcase name="header_template_null_safe_operators_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="281" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.007526"/>
        <testcase name="sidebar_template_null_safe_operators_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="303" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.007573"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutUserNameDisplayTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" tests="12" assertions="71" errors="0" failures="0" skipped="0" time="0.090329">
        <testcase name="client_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="44" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.010265"/>
        <testcase name="normal_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="62" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.006083"/>
        <testcase name="admin_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="80" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.006133"/>
        <testcase name="header_shows_actual_user_name_not_guest_for_all_user_types" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="98" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.010290"/>
        <testcase name="user_account_information_shows_actual_data_not_fallbacks" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="123" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="6" time="0.008018"/>
        <testcase name="logout_process_completes_successfully_with_proper_user_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="145" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="6" time="0.006052"/>
        <testcase name="role_based_visibility_still_works_with_corrected_user_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="165" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="8" time="0.007535"/>
        <testcase name="null_safe_operators_still_work_for_method_calls" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="189" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.005906"/>
        <testcase name="fallback_values_only_show_when_user_is_actually_null" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="208" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="3" time="0.003697"/>
        <testcase name="user_properties_vs_method_calls_handled_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="219" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="5" time="0.005318"/>
        <testcase name="all_user_types_can_logout_with_correct_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="239" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.011168"/>
        <testcase name="header_template_shows_correct_user_info_for_all_roles" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="270" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.009864"/>
      </testsuite>
      <testsuite name="Tests\Feature\SimpleFieldUtilityTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" tests="5" assertions="20" errors="0" failures="0" skipped="0" time="0.035326">
        <testcase name="field_utility_relationship_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" line="27" class="Tests\Feature\SimpleFieldUtilityTest" classname="Tests.Feature.SimpleFieldUtilityTest" assertions="4" time="0.008124"/>
        <testcase name="admin_can_create_field_with_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" line="44" class="Tests\Feature\SimpleFieldUtilityTest" classname="Tests.Feature.SimpleFieldUtilityTest" assertions="6" time="0.005221"/>
        <testcase name="field_show_page_loads_without_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" line="74" class="Tests\Feature\SimpleFieldUtilityTest" classname="Tests.Feature.SimpleFieldUtilityTest" assertions="3" time="0.007276"/>
        <testcase name="field_create_form_shows_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" line="90" class="Tests\Feature\SimpleFieldUtilityTest" classname="Tests.Feature.SimpleFieldUtilityTest" assertions="4" time="0.006921"/>
        <testcase name="field_edit_form_shows_utilities" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/SimpleFieldUtilityTest.php" line="106" class="Tests\Feature\SimpleFieldUtilityTest" classname="Tests.Feature.SimpleFieldUtilityTest" assertions="3" time="0.007784"/>
      </testsuite>
      <testsuite name="Tests\Feature\Unit\UtilityModelTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Unit/UtilityModelTest.php" tests="1" assertions="1" errors="0" failures="0" skipped="0" time="0.004605">
        <testcase name="test_example" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/Unit/UtilityModelTest.php" line="12" class="Tests\Feature\Unit\UtilityModelTest" classname="Tests.Feature.Unit.UtilityModelTest" assertions="1" time="0.004605"/>
      </testsuite>
      <testsuite name="Tests\Feature\UserBookingRelationshipTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" tests="5" assertions="16" errors="0" failures="0" skipped="0" time="0.025477">
        <testcase name="test_user_has_bookings_relationship" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" line="18" class="Tests\Feature\UserBookingRelationshipTest" classname="Tests.Feature.UserBookingRelationshipTest" assertions="4" time="0.003967"/>
        <testcase name="test_user_bookings_count" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" line="47" class="Tests\Feature\UserBookingRelationshipTest" classname="Tests.Feature.UserBookingRelationshipTest" assertions="2" time="0.003871"/>
        <testcase name="test_user_admin_bookings_relationship" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" line="71" class="Tests\Feature\UserBookingRelationshipTest" classname="Tests.Feature.UserBookingRelationshipTest" assertions="4" time="0.004531"/>
        <testcase name="test_features_page_can_access_user_bookings_count" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" line="101" class="Tests\Feature\UserBookingRelationshipTest" classname="Tests.Feature.UserBookingRelationshipTest" assertions="3" time="0.007143"/>
        <testcase name="test_features_page_works_for_users_with_no_bookings" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/UserBookingRelationshipTest.php" line="127" class="Tests\Feature\UserBookingRelationshipTest" classname="Tests.Feature.UserBookingRelationshipTest" assertions="3" time="0.005965"/>
      </testsuite>
    </testsuite>
  </testsuite>
</testsuites>
