<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ClientDashboardLayoutTest extends TestCase
{
    use RefreshDatabase;

    protected User $clientUser;

    protected User $employee;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientUser = User::factory()->create([
            'name' => 'Client User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $this->employee = User::factory()->create([
            'name' => 'Employee',
            'email' => '<EMAIL>',
            'role' => 'employee',
        ]);

        $this->adminUser = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }

    #[Test]
    public function client_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            ->assertViewIs('user.dashboard')
            ->assertSee('User Dashboard')
            ->assertSee('Welcome back, Client User!');
    }

    #[Test]
    public function client_dashboard_has_proper_layout_structure()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            // Check for welcome section with proper structure
            ->assertSee('Welcome back, Client User!')
            ->assertSee('Field Management System')

            // Check for Quick Actions section
            ->assertSee('Quick Actions')
            ->assertSee('View Profile')
            ->assertSee('Edit Settings')
            ->assertSee('Security')
            ->assertSee('Support')

            // Check for Features Grid
            ->assertSee('Profile Management')
            ->assertSee('Account Security')

            // Check for Account Information
            ->assertSee('Your Account Information')
            ->assertSee('User Access Level');
    }

    #[Test]
    public function client_dashboard_shows_appropriate_buttons_with_proper_spacing()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        // Verify that buttons are in dedicated sections, not next to user name
        $content = $response->getContent();

        // Check that user name is in welcome section
        $this->assertStringContainsString('Welcome back, Client User!', $content);

        // Check that action buttons are in separate Quick Actions section
        $this->assertStringContainsString('Quick Actions', $content);
        $this->assertStringContainsString('btn btn-outline-primary w-100', $content);

        // Verify proper card structure separates user name from action buttons
        $this->assertStringContainsString('card custom-card', $content);
    }

    #[Test]
    public function client_header_dropdown_shows_appropriate_links()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        // Client should see profile and settings, but NOT reservation links
        $response->assertSee('Profile')
            ->assertSee('My Profile')
            ->assertSee('Settings')
            ->assertDontSee('My Reservations')
            ->assertDontSee('New Reservation');

        // Check that Features navigation link is not present (but allow "features" in general text)
        $content = $response->getContent();
        $this->assertStringNotContainsString('href="'.route('employee.features').'"', $content);
    }

    #[Test]
    public function normal_user_header_dropdown_shows_reservation_links()
    {
        $response = $this->actingAs($this->employee)
            ->get(route('employee.dashboard'));

        $response->assertStatus(200);

        // Employee should see reservation links
        $response->assertSee('Profile')
            ->assertSee('My Reservations')
            ->assertSee('New Reservation')
            ->assertSee('Features');
    }

    #[Test]
    public function admin_header_dropdown_shows_reservation_links()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Admin should see reservation links
        $response->assertSee('Profile')
            ->assertSee('My Reservations')
            ->assertSee('New Reservation')
            ->assertSee('Features');
    }

    #[Test]
    public function client_dashboard_has_responsive_layout()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for responsive Bootstrap classes
        $this->assertStringContainsString('col-xl-12', $content);
        $this->assertStringContainsString('col-xl-6', $content);
        $this->assertStringContainsString('col-lg-6', $content);
        $this->assertStringContainsString('col-md-6', $content);
        $this->assertStringContainsString('col-xl-3 col-lg-4 col-md-6', $content);
    }

    #[Test]
    public function client_dashboard_quick_actions_are_properly_spaced()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that Quick Actions section has proper spacing classes
        $this->assertStringContainsString('row gy-3', $content);
        $this->assertStringContainsString('btn btn-outline-primary w-100', $content);
        $this->assertStringContainsString('btn btn-outline-success w-100', $content);
        $this->assertStringContainsString('btn btn-outline-info w-100', $content);
        $this->assertStringContainsString('btn btn-outline-warning w-100', $content);
    }

    #[Test]
    public function client_dashboard_feature_cards_have_proper_button_layout()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that feature cards have proper button spacing
        $this->assertStringContainsString('d-flex gap-2', $content);
        $this->assertStringContainsString('btn btn-primary btn-sm', $content);
        $this->assertStringContainsString('btn btn-outline-primary btn-sm', $content);
        $this->assertStringContainsString('btn btn-success btn-sm', $content);
        $this->assertStringContainsString('btn btn-outline-success btn-sm', $content);
    }

    #[Test]
    public function client_dashboard_welcome_section_has_proper_icon_spacing()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check welcome section structure with proper spacing
        $this->assertStringContainsString('d-flex align-items-center', $content);
        $this->assertStringContainsString('avatar avatar-lg bg-primary-transparent', $content);
        $this->assertStringContainsString('ti ti-user-check fs-18', $content);
        $this->assertStringContainsString('me-3', $content);
    }

    #[Test]
    public function client_dashboard_access_notice_is_informative()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            ->assertSee('User Access Level')
            ->assertSee('personal profile')
            ->assertSee('account settings')
            ->assertSee('contact your administrator')
            ->assertSee('support button above');
    }

    #[Test]
    public function client_dashboard_support_function_is_included()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that support JavaScript function is included
        $this->assertStringContainsString('function contactSupport()', $content);
        $this->assertStringContainsString('onclick="contactSupport()"', $content);
    }

    #[Test]
    public function client_dashboard_maintains_consistency_with_other_dashboards()
    {
        // Test client dashboard
        $clientResponse = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        // Test employee dashboard
        $normalResponse = $this->actingAs($this->employee)
            ->get(route('employee.dashboard'));

        // Both should use similar layout patterns
        $clientContent = $clientResponse->getContent();
        $normalContent = $normalResponse->getContent();

        // Both should have welcome sections
        $this->assertStringContainsString('Welcome back,', $clientContent);
        $this->assertStringContainsString('Welcome,', $normalContent);

        // Both should have account information sections
        $this->assertStringContainsString('Your Account Information', $clientContent);
        $this->assertStringContainsString('Your Account Information', $normalContent);

        // Both should use similar card structures
        $this->assertStringContainsString('card custom-card', $clientContent);
        $this->assertStringContainsString('card custom-card', $normalContent);
    }
}
