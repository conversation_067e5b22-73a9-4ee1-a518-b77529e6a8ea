<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AdminFieldShowPageTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $field;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create(['role' => 'admin']);

        // Create a test field with ID 11 (or close to it)
        $this->field = Field::factory()->create([
            'name' => 'Test Soccer Field 11',
            'type' => 'Soccer',
            'description' => 'Professional soccer field with artificial turf and LED lighting for testing field ID 11',
            'hourly_rate' => 85.00,
            'capacity' => 24,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        // Create amenities and attach them to the field
        $amenities = [
            ['name' => 'Lighting', 'icon_class' => 'ri-lightbulb-line'],
            ['name' => 'Parking', 'icon_class' => 'ri-car-line'],
            ['name' => 'Restrooms', 'icon_class' => 'ri-home-line'],
            ['name' => 'Scoreboard', 'icon_class' => 'ri-dashboard-line'],
        ];

        foreach ($amenities as $amenityData) {
            $amenity = \App\Models\Amenity::factory()->create($amenityData);
            $this->field->amenities()->attach($amenity->id);
        }
    }

    #[Test]
    public function field_id_11_database_check()
    {
        // Check if we have any fields
        $fieldCount = Field::count();
        echo "Total fields in database: $fieldCount\n";

        // List all field IDs and names
        $fields = Field::all(['id', 'name']);
        echo "Available fields:\n";
        foreach ($fields as $field) {
            echo "- ID: {$field->id}, Name: {$field->name}\n";
        }

        // Check if field 11 specifically exists
        $field11 = Field::find(11);
        if ($field11) {
            echo "✅ Field ID 11 exists: {$field11->name}\n";
        } else {
            echo "❌ Field ID 11 does not exist\n";

            // Create a field with ID 11 for testing
            $testField = Field::factory()->create([
                'id' => 11,
                'name' => 'Test Field 11',
                'type' => 'Soccer',
                'description' => 'Test field for troubleshooting',
                'hourly_rate' => 75.00,
                'capacity' => 22,
                'status' => 'Active',
            ]);

            // Create amenities and attach them to the test field
            $amenities = [
                ['name' => 'Test Lighting 11', 'icon_class' => 'ri-lightbulb-line'],
                ['name' => 'Test Parking 11', 'icon_class' => 'ri-car-line'],
            ];

            foreach ($amenities as $amenityData) {
                $amenity = \App\Models\Amenity::factory()->create($amenityData);
                $testField->amenities()->attach($amenity->id);
            }
            echo "✅ Created test field with ID 11: {$testField->name}\n";
        }

        $this->assertTrue(true); // Always pass to see output
    }

    #[Test]
    public function admin_can_access_field_show_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);
        echo "✅ Field show page loads successfully\n";

        // Check for layout errors
        $response->assertDontSee('View [layouts.app] not found');
        $response->assertDontSee('x-app-layout');
        echo "✅ No layout errors detected\n";

        // Check basic content
        $response->assertSee($this->field->name);
        $response->assertSee($this->field->type);
        echo "✅ Basic field information displays\n";
    }

    #[Test]
    public function field_show_page_has_correct_layout()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for admin layout elements
        $response->assertSee('Field Details');
        $response->assertSee('breadcrumb');
        $response->assertSee('Home');
        $response->assertSee('Fields');

        echo "✅ Admin layout and breadcrumbs present\n";
    }

    #[Test]
    public function field_show_page_displays_all_content()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Basic Information
        $response->assertSee($this->field->name);
        $response->assertSee($this->field->type);
        $response->assertSee($this->field->description);
        $response->assertSee($this->field->status);
        echo "✅ Basic information displays correctly\n";

        // Pricing and Capacity
        $response->assertSee('$'.number_format($this->field->hourly_rate, 2));
        $response->assertSee($this->field->capacity.' people');
        echo "✅ Pricing and capacity display correctly\n";

        // Amenities
        $response->assertSee('Available Amenities');
        echo "✅ Amenities section present\n";

        // Navigation buttons
        $response->assertSee('Edit Field');
        $response->assertSee('Delete Field');
        $response->assertSee('Back to Fields');
        echo "✅ Navigation buttons present\n";
    }

    #[Test]
    public function field_show_page_navigation_buttons_work()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for proper route URLs in buttons
        $response->assertSee(route('admin.fields.edit', $this->field));
        $response->assertSee(route('admin.fields.index'));
        echo "✅ Navigation button URLs are correct\n";

        // Check for delete modal
        $response->assertSee('deleteModal');
        $response->assertSee('confirmDelete');
        echo "✅ Delete modal functionality present\n";
    }
}
